<!DOCTYPE html>

<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="light-style layout-navbar-fixed layout-menu-fixed layout-compact" dir="ltr" data-theme="theme-default" data-assets-path="/assets/" data-template="">
    <head>
        
        <?php echo $__env->make('layouts.administration.partials.metas', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        
        
        <title><?php echo e(config('app.name')); ?> || <?php echo $__env->yieldContent('page_title'); ?></title>
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset(config('app.favicon'))); ?>" />

        <!-- Start css -->
        <?php echo $__env->make('layouts.administration.partials.stylesheet', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End css -->
    </head>

    <body>
        <!-- Layout wrapper -->
        <div class="layout-wrapper layout-content-navbar">
            <div class="layout-container">
                <!-- Menu -->
                <!-- Start Sidebar -->
                <?php echo $__env->make('layouts.administration.partials.sidenav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End Sidebar -->
                <!-- / Menu -->

                <!-- Layout container -->
                <div class="layout-page">
                    <!-- Start Top Navbar -->
                    <?php echo $__env->make('layouts.administration.partials.topnav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <!-- End Top Navbar -->

                    <!-- Content wrapper -->
                    <div class="content-wrapper">
                        <!-- Content -->

                        <div class="container-xxl flex-grow-1 container-p-y">
                            <!-- Start Breadcrumbbar -->
                            <?php echo $__env->make('layouts.administration.partials.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <!-- End Breadcrumbbar -->
                            
                            <?php if($errors->any()): ?>
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="alert alert-danger alert-dismissible" role="alert">
                                                <i class="ti ti-ban mr-3" style="margin-top: -3px;"></i>
                                                <?php echo e($error); ?>

                                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if(session('error')): ?>
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="alert alert-danger alert-dismissible" role="alert">
                                            <i class="ti ti-ban mr-3" style="margin-top: -3px;"></i>
                                            <?php echo e(session('error')); ?>

                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Start row -->
                            <?php echo $__env->yieldContent('content'); ?>
                            <!-- End row -->
                        </div>
                        <!-- / Content -->

                        <!-- Start Footerbar -->
                        
                        <!-- End Footerbar -->

                        <div class="content-backdrop fade"></div>
                    </div>
                    <!-- Content wrapper -->
                </div>
                <!-- / Layout page -->
            </div>

            <!-- Overlay -->
            <div class="layout-overlay layout-menu-toggle"></div>

            <!-- Drag Target Area To SlideIn Menu On Small Screens -->
            <div class="drag-target"></div>
        </div>
        <!-- / Layout wrapper -->

        <!-- Start js -->
        <?php echo $__env->make('layouts.administration.partials.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End js -->

        
        <?php echo $__env->make('sweetalert::alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </body>
</html><?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/app.blade.php ENDPATH**/ ?>