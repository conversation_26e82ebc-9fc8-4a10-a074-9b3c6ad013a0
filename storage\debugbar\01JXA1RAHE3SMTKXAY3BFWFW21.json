{"__meta": {"id": "01JXA1RAHE3SMTKXAY3BFWFW21", "datetime": "2025-06-09 15:53:41", "utime": **********.424576, "method": "GET", "uri": "/dashboard/calendar/events?start=2025-05-31T00%3A00%3A00%2B06%3A00&end=2025-07-12T00%3A00%3A00%2B06%3A00", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749462819.073844, "end": **********.424613, "duration": 2.35076904296875, "duration_str": "2.35s", "measures": [{"label": "Booting", "start": 1749462819.073844, "relative_start": 0, "end": **********.014333, "relative_end": **********.014333, "duration": 1.****************, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.014356, "relative_start": 1.**************, "end": **********.424617, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "410ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.075335, "relative_start": 2.***************, "end": **********.085833, "relative_end": **********.085833, "duration": 0.************, "duration_str": "10.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.417706, "relative_start": 2.****************, "end": **********.418773, "relative_end": **********.418773, "duration": 0.0010669231414794922, "duration_str": "1.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET dashboard/calendar/events", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "controller": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.dashboard.calendar.events", "prefix": "/dashboard/calendar", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php:42-83</a>"}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02901, "accumulated_duration_str": "29.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.199702, "duration": 0.009439999999999999, "duration_str": "9.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 32.541}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.226607, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 32.541, "width_percent": 3.482}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.240018, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 36.022, "width_percent": 2.482}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.24615, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 38.504, "width_percent": 2.585}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 23 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [23, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.265223, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 41.089, "width_percent": 4.343}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": **********.27386, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 45.433, "width_percent": 2.378}, {"sql": "select * from `tasks` where `creator_id` = 23 and `deadline` is not null and `deadline` between '2025-05-31T00:00:00+06:00' and '2025-07-12T00:00:00+06:00' and `tasks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [23, "2025-05-31T00:00:00+06:00", "2025-07-12T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 101}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.282554, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:101", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=101", "ajax": false, "filename": "DashboardCalendarController.php", "line": "101"}, "connection": "blueorange", "explain": null, "start_percent": 47.811, "width_percent": 10.376}, {"sql": "select * from `tasks` where exists (select * from `users` inner join `task_user` on `users`.`id` = `task_user`.`user_id` where `tasks`.`id` = `task_user`.`task_id` and `user_id` = 23 and `users`.`deleted_at` is null) and `deadline` is not null and `deadline` between '2025-05-31T00:00:00+06:00' and '2025-07-12T00:00:00+06:00' and `tasks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [23, "2025-05-31T00:00:00+06:00", "2025-07-12T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 126}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.293053, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:126", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 126}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=126", "ajax": false, "filename": "DashboardCalendarController.php", "line": "126"}, "connection": "blueorange", "explain": null, "start_percent": 58.187, "width_percent": 12.927}, {"sql": "select * from `holidays` where `is_active` = 1 and `date` between '2025-05-31T00:00:00+06:00' and '2025-07-12T00:00:00+06:00' and `holidays`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "2025-05-31T00:00:00+06:00", "2025-07-12T00:00:00+06:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 161}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 68}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.304645, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:161", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=161", "ajax": false, "filename": "DashboardCalendarController.php", "line": "161"}, "connection": "blueorange", "explain": null, "start_percent": 71.113, "width_percent": 7.859}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 23 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [23, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 260}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 193}], "start": **********.3336, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 78.973, "width_percent": 9.1}, {"sql": "select * from `leave_histories` where `status` = 'Approved' and `date` between '2025-05-31T00:00:00+06:00' and '2025-07-12T00:00:00+06:00' and `user_id` = 23 and `leave_histories`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Approved", "2025-05-31T00:00:00+06:00", "2025-07-12T00:00:00+06:00", 23], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 209}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 71}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.350897, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "DashboardCalendarController.php:209", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=209", "ajax": false, "filename": "DashboardCalendarController.php", "line": "209"}, "connection": "blueorange", "explain": null, "start_percent": 88.073, "width_percent": 7.928}, {"sql": "select `day` from `weekends` where `is_active` = 1 and `weekends`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Weekend/Weekend.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\Weekend\\Weekend.php", "line": 26}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 254}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3624692, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Weekend.php:26", "source": {"index": 14, "namespace": null, "name": "app/Models/Weekend/Weekend.php", "file": "D:\\Nigel\\laragon\\www\\BlueOrange\\app\\Models\\Weekend\\Weekend.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FWeekend%2FWeekend.php&line=26", "ajax": false, "filename": "Weekend.php", "line": "26"}, "connection": "blueorange", "explain": null, "start_percent": 96.001, "width_percent": 3.999}]}, "models": {"data": {"App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "edG9C09JKf5BGywKCFiWMOi9NAywQ1cbB9twtDb1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "23", "auth": "array:1 [\n  \"password_confirmed_at\" => 1749460374\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/dashboard/calendar/events?end=2025-07-12T00%3A00%3A00%2B06%3A00&start=2025-0...", "action_name": "administration.dashboard.calendar.events", "controller_action": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents", "uri": "GET dashboard/calendar/events", "controller": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/dashboard/calendar", "file": "<a href=\"phpstorm://open?file=D%3A%2FNigel%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FDashboard%2FDashboardCalendarController.php&line=42\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Dashboard/DashboardCalendarController.php:42-83</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip", "duration": "2.36s", "peak_memory": "28MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-187524153 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str title=\"25 characters\">2025-05-31T00:00:00+06:00</span>\"\n  \"<span class=sf-dump-key>end</span>\" => \"<span class=sf-dump-str title=\"25 characters\">2025-07-12T00:00:00+06:00</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-187524153\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-610777719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610777719\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-862830210 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6IldKdXNjSkQ0T0N5L0ttaVFKa05uM0E9PSIsInZhbHVlIjoiUm4yZ012MkE3OUVEblZjT1VYSFBtaVl0eEw5U3FOQm5SOUZZOC9KM1ovWWV1bHFseDVUaHJKVGNWd2kwaHJmMDBQbU9MMkxzRTdMcUhWajFHVEEwekhKS3Z0OEtIeGJZTXZ0Y2dUcDlhbWdXTGpaNTdNMnlnNml5dnNvNjE3REQiLCJtYWMiOiIyMGE0ODYwNjRhYjAwMzNmNTVjYjJkZTVmZTU0MmJhMWQyMTIxOTNlZmY2NGJkYWExMTdhYzRiMmQ3ZWUwYWU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjFXS1NmNml0S3FobDB6WEd2bnFRNFE9PSIsInZhbHVlIjoiZ1U2cG1ZTkNmYlZrV2xBMHNnRWZBVkNFLy8ybUNFRnlSajhOVWFzcDQ0dTcvS25iTXZ1QVhyZXEzNnFOSmxIYjhBdFMvY1dPLzc2b3ZrMkMrQXJCbmpqWUtJMDluWXgwcUx1Y0hpYWlkUGVrZGpGS2gxdnpLNEV2Skp1NldneGYiLCJtYWMiOiJjNGE5Zjg5MDQzZTEwYjA4YjRiNDRlMmVmZTZiY2I4MjJkNDUxYzJiMzE2YTAwZDM4ZGY2YjI1MWM4NmRhYjgwIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlZ5cms5T3Y4dEZPdWlBaUZaQzNZdWc9PSIsInZhbHVlIjoiaTdiRE9SK1h1K0l6dUdwVGFUL3h4aDlPV0NSaFNQNCtSc0xmd1g0WHE1bnFJMnVRdVlnRDlXSG9zUWsxTVlSNkxHS3FxcGZoRllOcjJWSnI4cDBUZTZ1SVUxY1dmOVUyWVhJOGN2RlpNUGIzZ1dKYUpoOXJaL3JBRFl0eDJMRmciLCJtYWMiOiI3ZjMwMzZlN2JiZDNkZmRkYzI3MTQ4NTFlZWU4OGM2MzBmMjVjMGJiYjcwZWViMjJiMDU3NjliNTFmZGNkMzgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-862830210\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1352340275 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">euT24gYlMMaxfRdZx7xbC4ZtLo6J3p4mf0lhmV1h</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">edG9C09JKf5BGywKCFiWMOi9NAywQ1cbB9twtDb1</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Myfwk42j1LZtYbXaWLMs6q72y0r0FHB7gowgc7Hw</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352340275\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1359596825 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 09 Jun 2025 09:53:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359596825\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2054932502 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">edG9C09JKf5BGywKCFiWMOi9NAywQ1cbB9twtDb1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">https://blueorange.test/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1749460374</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054932502\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/dashboard/calendar/events?end=2025-07-12T00%3A00%3A00%2B06%3A00&start=2025-0...", "action_name": "administration.dashboard.calendar.events", "controller_action": "App\\Http\\Controllers\\Administration\\Dashboard\\DashboardCalendarController@getEvents"}, "badge": null}}