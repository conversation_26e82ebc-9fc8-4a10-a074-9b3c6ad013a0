1749462886O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:33:"App\Models\Chatting\GroupChatting":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"group_chattings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:61;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:1;s:7:"message";s:19:"how about you mate?";s:11:"reply_to_id";i:56;s:10:"created_at";s:19:"2025-05-16 21:10:34";s:10:"updated_at";s:19:"2025-05-16 21:10:34";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:61;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:1;s:7:"message";s:19:"how about you mate?";s:11:"reply_to_id";i:56;s:10:"created_at";s:19:"2025-05-16 21:10:34";s:10:"updated_at";s:19:"2025-05-16 21:10:34";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"message";s:40:"Stevebauman\Purify\Casts\PurifyHtmlOnGet";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:6:"sender";O:15:"App\Models\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:6:"userid";s:11:"UID00000001";s:10:"first_name";s:6:"System";s:9:"last_name";s:9:"Developer";s:4:"name";s:16:"System Developer";s:5:"email";s:18:"<EMAIL>";s:17:"email_verified_at";s:19:"2025-02-01 02:43:37";s:8:"password";s:60:"$2y$12$sGCx7hK6SXzEOfqUek81uep13KuZGl7SEQY0JLfJunAVOx7cNT5JW";s:6:"status";s:6:"Active";s:14:"remember_token";s:60:"78CbFRK4fNGwiKnJfWjYTtp2nauDN6oBzChhoaAdAFwHaQJfuNV0XsLVhpjs";s:10:"created_at";s:19:"2025-02-01 02:43:37";s:10:"updated_at";s:19:"2025-05-08 14:11:22";s:10:"deleted_at";N;}s:11:" * original";a:13:{s:2:"id";i:1;s:6:"userid";s:11:"UID00000001";s:10:"first_name";s:6:"System";s:9:"last_name";s:9:"Developer";s:4:"name";s:16:"System Developer";s:5:"email";s:18:"<EMAIL>";s:17:"email_verified_at";s:19:"2025-02-01 02:43:37";s:8:"password";s:60:"$2y$12$sGCx7hK6SXzEOfqUek81uep13KuZGl7SEQY0JLfJunAVOx7cNT5JW";s:6:"status";s:6:"Active";s:14:"remember_token";s:60:"78CbFRK4fNGwiKnJfWjYTtp2nauDN6oBzChhoaAdAFwHaQJfuNV0XsLVhpjs";s:10:"created_at";s:19:"2025-02-01 02:43:37";s:10:"updated_at";s:19:"2025-05-08 14:11:22";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:17:"email_verified_at";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"userid";i:1;s:10:"first_name";i:2;s:9:"last_name";i:3;s:4:"name";i:4;s:5:"email";i:5;s:8:"password";i:6;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:17:" * cascadeDeletes";a:0:{}s:8:" * dates";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:10:"deleted_at";}s:14:" * accessToken";N;s:16:" * forceDeleting";b:0;}s:5:"group";O:33:"App\Models\Chatting\ChattingGroup":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"chatting_groups";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:1;s:7:"groupid";s:18:"CGID20250515182901";s:4:"name";s:19:"Test Chatting Group";s:10:"creator_id";i:1;s:10:"created_at";s:19:"2025-05-15 18:29:01";s:10:"updated_at";s:19:"2025-05-15 18:29:01";s:10:"deleted_at";N;}s:11:" * original";a:7:{s:2:"id";i:1;s:7:"groupid";s:18:"CGID20250515182901";s:4:"name";s:19:"Test Chatting Group";s:10:"creator_id";i:1;s:10:"created_at";s:19:"2025-05-15 18:29:01";s:10:"updated_at";s:19:"2025-05-15 18:29:01";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"groupid";i:1;s:4:"name";i:2;s:10:"creator_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:0:{}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";i:3;s:11:"reply_to_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:1:{i:0;s:11:"group_users";}s:16:" * forceDeleting";b:0;}i:1;O:33:"App\Models\Chatting\GroupChatting":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"group_chattings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:60;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:1;s:7:"message";s:9:"I am fine";s:11:"reply_to_id";i:56;s:10:"created_at";s:19:"2025-05-16 16:23:14";s:10:"updated_at";s:19:"2025-05-16 16:23:14";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:60;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:1;s:7:"message";s:9:"I am fine";s:11:"reply_to_id";i:56;s:10:"created_at";s:19:"2025-05-16 16:23:14";s:10:"updated_at";s:19:"2025-05-16 16:23:14";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"message";s:40:"Stevebauman\Purify\Casts\PurifyHtmlOnGet";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:6:"sender";r:45;s:5:"group";r:122;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";i:3;s:11:"reply_to_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:1:{i:0;s:11:"group_users";}s:16:" * forceDeleting";b:0;}i:2;O:33:"App\Models\Chatting\GroupChatting":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"group_chattings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:59;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:8:"Hi Hello";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 16:21:06";s:10:"updated_at";s:19:"2025-05-16 16:21:06";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:59;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:8:"Hi Hello";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 16:21:06";s:10:"updated_at";s:19:"2025-05-16 16:21:06";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"message";s:40:"Stevebauman\Purify\Casts\PurifyHtmlOnGet";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:6:"sender";O:15:"App\Models\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:2;s:6:"userid";s:11:"UID00000002";s:10:"first_name";s:4:"Demo";s:9:"last_name";s:11:"Super admin";s:4:"name";s:16:"Demo Super Admin";s:5:"email";s:19:"<EMAIL>";s:17:"email_verified_at";s:19:"2025-02-01 02:43:37";s:8:"password";s:60:"$2y$10$yVT3RE4Sgh/PVxJ.DjMA.eVkQYMMZUnM2..TixhN5xyu5wji4GtDa";s:6:"status";s:6:"Active";s:14:"remember_token";s:60:"4IqvJxLiqgBTzgk8Ft1cqrePRL3rdfDT1DsPxbYvnRywojrPID4styBReZD8";s:10:"created_at";s:19:"2025-02-01 02:43:37";s:10:"updated_at";s:19:"2025-05-08 15:50:12";s:10:"deleted_at";N;}s:11:" * original";a:13:{s:2:"id";i:2;s:6:"userid";s:11:"UID00000002";s:10:"first_name";s:4:"Demo";s:9:"last_name";s:11:"Super admin";s:4:"name";s:16:"Demo Super Admin";s:5:"email";s:19:"<EMAIL>";s:17:"email_verified_at";s:19:"2025-02-01 02:43:37";s:8:"password";s:60:"$2y$10$yVT3RE4Sgh/PVxJ.DjMA.eVkQYMMZUnM2..TixhN5xyu5wji4GtDa";s:6:"status";s:6:"Active";s:14:"remember_token";s:60:"4IqvJxLiqgBTzgk8Ft1cqrePRL3rdfDT1DsPxbYvnRywojrPID4styBReZD8";s:10:"created_at";s:19:"2025-02-01 02:43:37";s:10:"updated_at";s:19:"2025-05-08 15:50:12";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:17:"email_verified_at";s:8:"datetime";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:7:{i:0;s:6:"userid";i:1;s:10:"first_name";i:2;s:9:"last_name";i:3;s:4:"name";i:4;s:5:"email";i:5;s:8:"password";i:6;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:17:" * cascadeDeletes";a:0:{}s:8:" * dates";a:3:{i:0;s:10:"created_at";i:1;s:10:"updated_at";i:2;s:10:"deleted_at";}s:14:" * accessToken";N;s:16:" * forceDeleting";b:0;}s:5:"group";r:122;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";i:3;s:11:"reply_to_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:1:{i:0;s:11:"group_users";}s:16:" * forceDeleting";b:0;}i:3;O:33:"App\Models\Chatting\GroupChatting":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"group_chattings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:58;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:8:"Hi Buddy";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 15:37:32";s:10:"updated_at";s:19:"2025-05-16 15:37:32";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:58;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:8:"Hi Buddy";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 15:37:32";s:10:"updated_at";s:19:"2025-05-16 15:37:32";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"message";s:40:"Stevebauman\Purify\Casts\PurifyHtmlOnGet";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:6:"sender";r:290;s:5:"group";r:122;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";i:3;s:11:"reply_to_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:1:{i:0;s:11:"group_users";}s:16:" * forceDeleting";b:0;}i:4;O:33:"App\Models\Chatting\GroupChatting":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"group_chattings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:8:{s:2:"id";i:57;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:7:"Hi Mate";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 15:30:36";s:10:"updated_at";s:19:"2025-05-16 15:30:36";s:10:"deleted_at";N;}s:11:" * original";a:8:{s:2:"id";i:57;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";s:7:"Hi Mate";s:11:"reply_to_id";N;s:10:"created_at";s:19:"2025-05-16 15:30:36";s:10:"updated_at";s:19:"2025-05-16 15:30:36";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:7:"message";s:40:"Stevebauman\Purify\Casts\PurifyHtmlOnGet";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:6:"sender";r:290;s:5:"group";r:122;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:4:{i:0;s:17:"chatting_group_id";i:1;s:9:"sender_id";i:2;s:7:"message";i:3;s:11:"reply_to_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:17:" * cascadeDeletes";a:1:{i:0;s:11:"group_users";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}