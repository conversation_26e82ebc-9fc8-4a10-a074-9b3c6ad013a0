<!-- Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&ampdisplay=swap" rel="stylesheet" />

<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/fonts/tabler-icons.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/fonts/fontawesome.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/fonts/flag-icons.css')); ?>" />

<!-- Core CSS -->
<link rel="stylesheet" class="template-customizer-core-css" href="<?php echo e(asset('assets/vendor/css/rtl/core.css')); ?>" />
<link rel="stylesheet" class="template-customizer-theme-css" href="<?php echo e(asset('assets/vendor/css/rtl/theme-default.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/css/demo.css')); ?>" />

<!-- Vendors CSS -->
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/node-waves/node-waves.css')); ?>" />
<link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css')); ?>" />

<link rel="stylesheet" href="<?php echo e(asset('assets/css/custom_css/jquery-confirm/jquery-confirm.min.css')); ?>">


<link rel="stylesheet" href="<?php echo e(asset('assets/css/custom.css')); ?>" />

<!-- Page CSS -->
<?php echo $__env->yieldContent('css_links'); ?>

<!-- Helpers -->
<script src="<?php echo e(asset('assets/vendor/js/helpers.js')); ?>"></script>
<!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->
<!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
<script src="<?php echo e(asset('assets/vendor/js/template-customizer.js')); ?>"></script>
<!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
<script src="<?php echo e(asset('assets/js/config.js')); ?>"></script>

<?php echo $__env->yieldContent('custom_css'); ?><?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/stylesheet.blade.php ENDPATH**/ ?>