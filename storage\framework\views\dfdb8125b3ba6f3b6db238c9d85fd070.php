<!-- Daily Break Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Daily Break Create', 'Daily Break Read'])): ?>
<li class="menu-item <?php echo e(request()->is('daily_break*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-clock-play"></i>
        <div data-i18n="Daily Break"><?php echo e(__('Daily Break')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Daily Break Update', 'Daily Break Delete'])): ?>
            <li class="menu-item <?php echo e(request()->is('daily_break/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_break.index')); ?>" class="menu-link"><?php echo e(__('All Daily Breaks')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Break Read')): ?>
            <li class="menu-item <?php echo e(request()->is('daily_break/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_break.my')); ?>" class="menu-link"><?php echo e(__('My Daily Breaks')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Break Create')): ?>
            <li class="menu-item <?php echo e(request()->is('daily_break/start_stop*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_break.create')); ?>" class="menu-link"><?php echo e(__('Start/Stop Break')); ?></a>
            </li>

            <li class="menu-item <?php echo e(request()->is('daily_break/barcode*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_break.barcode.scanner')); ?>" class="menu-link"><?php echo e(__('Bar Code Break')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/daily_break.blade.php ENDPATH**/ ?>