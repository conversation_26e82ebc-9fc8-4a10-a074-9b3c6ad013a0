<div class="col-md-4">
    <div class="card card-action card-border-shadow-success mb-1">
        <div class="card-header collapsed">
            <div class="card-action-title"><?php echo e(__('Currently Working')); ?></div>
            <div class="card-action-element">
                <ul class="list-inline mb-0">
                    <li class="list-inline-item">
                        <a href="javascript:void(0);" class="card-collapsible">
                            <i class="tf-icons ti ti-chevron-right scaleX-n1-rtl ti-sm"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="collapse">
            <div class="card-body pt-0">
                <div class="d-flex align-items-center flex-wrap">
                    <?php $__empty_1 = true; $__currentLoopData = $currentlyWorkingUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $workingUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <?php
                            // Get the current attendance more safely
                            $currentAttendance = $workingUser->attendances->first();

                            // Fallback: if no attendance is loaded, query directly
                            if (!$currentAttendance) {
                                $currentAttendance = \App\Models\Attendance\Attendance::where('user_id', $workingUser->id)
                                    ->whereNull('clock_out')
                                    ->whereNotNull('clock_in')
                                    ->orderBy('clock_in', 'desc')
                                    ->first();
                            }

                            $clockInTime = $currentAttendance ? show_time($currentAttendance->clock_in) : '';
                            $tooltipText = ($workingUser->employee->alias_name ?? $workingUser->name) . ($clockInTime ? ' (' . $clockInTime . ')' : '');
                        ?>
                        <div class="avatar me-2 mb-2 avatar-online" title="<?php echo e($tooltipText); ?>">
                            <?php if($workingUser->getFirstMediaUrl('avatar')): ?>
                                <img src="<?php echo e($workingUser->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="<?php echo e($workingUser->name); ?>" class="rounded-circle" />
                            <?php else: ?>
                                <span class="avatar-initial rounded-circle bg-label-primary"><?php echo e(substr($workingUser->name, 0, 1)); ?></span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center w-100 py-3">
                            <p class="mb-0 text-muted">No users currently working</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/administration/dashboard/partials/_currently_working.blade.php ENDPATH**/ ?>