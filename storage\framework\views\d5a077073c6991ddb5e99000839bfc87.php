<!-- Vault Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Vault Create', 'Vault Read'])): ?>
<li class="menu-item <?php echo e(request()->is('vault*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-lock-square"></i>
        <div data-i18n="Vault"><?php echo e(__('Vault')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Vault Read'])): ?>
            <li class="menu-item <?php echo e(request()->is('vault/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.vault.index')); ?>" class="menu-link"><?php echo e(__('All Credentials')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Vault Create')): ?>
            <li class="menu-item <?php echo e(request()->is('vault/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.vault.create')); ?>" class="menu-link"><?php echo e(__('Store Credential')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/vault.blade.php ENDPATH**/ ?>