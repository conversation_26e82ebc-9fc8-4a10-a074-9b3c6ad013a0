<nav class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme" id="layout-navbar">
    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
            <i class="ti ti-menu-2 ti-sm"></i>
        </a>
    </div>

    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
        <!-- Light-Dark Mode -->
        <div class="navbar-nav align-items-center">
            <div class="nav-item dropdown-style-switcher dropdown me-2 me-xl-0">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class="ti ti-md"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-start dropdown-styles">
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="light">
                            <span class="align-middle"><i class="ti ti-sun me-2"></i>Light</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="dark">
                            <span class="align-middle"><i class="ti ti-moon me-2"></i>Dark</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="system">
                            <span class="align-middle"><i class="ti ti-device-desktop me-2"></i>System</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- /Light-Dark Mode -->

        <ul class="navbar-nav flex-row align-items-center ms-auto">
            <!-- Language -->
            <li class="nav-item dropdown-language dropdown me-2 me-xl-0">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class="ti ti-language rounded-circle ti-md"></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php $__currentLoopData = config('localization.languages'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo e(route('administration.localization', ['lang' => $lang['key']])); ?>" data-language="<?php echo e($lang['key']); ?>">
                                <span class="align-middle"><?php echo e($lang['value']); ?></span>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </li>
            <!--/ Language -->

            <!-- Shortcut links  -->
            <li class="nav-item dropdown-shortcuts navbar-dropdown dropdown me-2 me-xl-0">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
                    <i class="ti ti-share-3 ti-md"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-end py-0">
                    <div class="dropdown-menu-header border-bottom">
                        <div class="dropdown-header d-flex align-items-center py-3">
                            <h5 class="text-body mb-0 me-auto"><?php echo e(__('topnav.shortcuts')); ?></h5>
                            <a href="<?php echo e(route('administration.shortcut.create')); ?>" class="dropdown-shortcuts-add text-body" data-bs-toggle="tooltip" data-bs-placement="top" title="<?php echo e(__('topnav.add_shortcut')); ?>"><i class="ti ti-sm ti-plus"></i></a>
                        </div>
                    </div>
                    <div class="dropdown-shortcuts-list scrollable-container">
                        <?php $__currentLoopData = auth()->user()->shortcuts->chunk(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chunk): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="row row-bordered overflow-visible g-0">
                                <?php $__currentLoopData = $chunk; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shortcut): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="dropdown-shortcuts-item col">
                                        <span class="dropdown-shortcuts-icon rounded-circle mb-2">
                                            <i class="ti ti-<?php echo e($shortcut->icon); ?> fs-4"></i>
                                        </span>
                                        <a href="<?php echo e($shortcut->url); ?>" class="stretched-link"><?php echo e($shortcut->name); ?></a>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </li>
            <!-- Shortcut links -->

            <!-- Notification -->
            <li class="nav-item dropdown-notifications navbar-dropdown dropdown me-3 me-xl-1">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
                    <i class="ti ti-bell ti-md"></i>
                    <?php if(auth()->user()->unreadNotifications->count() > 0): ?>
                        <span class="badge bg-danger rounded-pill badge-notifications">
                            <?php echo e(auth()->user()->unreadNotifications->count()); ?>

                        </span>
                    <?php endif; ?>
                </a>
                <ul class="dropdown-menu dropdown-menu-end py-0">
                    <li class="dropdown-menu-header border-bottom">
                        <div class="dropdown-header d-flex align-items-center py-3">
                            <h5 class="text-body mb-0 me-auto"><?php echo e(__('topnav.notifications')); ?></h5>
                            <?php if(auth()->user()->unreadNotifications->count() > 0): ?>
                                <a href="<?php echo e(route('administration.notification.mark_all_as_read')); ?>" class="dropdown-notifications-all text-body confirm-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Mark all as read">
                                    <i class="ti ti-mail-opened fs-4"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </li>
                    <li class="dropdown-notifications-list scrollable-container">
                        <ul class="list-group list-group-flush">
                            <?php $__empty_1 = true; $__currentLoopData = auth()->user()->unreadNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <li class="list-group-item list-group-item-action dropdown-notifications-item">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0 me-3">
                                            <div class="avatar">
                                                <span class="avatar-initial rounded-circle bg-label-primary">
                                                    <?php if(isset($notification->data['icon'])): ?>
                                                        <i class="ti ti-<?php echo e($notification->data['icon']); ?> ti-md"></i>
                                                    <?php else: ?>
                                                        <i class="ti ti-bell ti-md"></i>
                                                    <?php endif; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <a href="<?php echo e(route('administration.notification.mark_as_read_and_redirect', ['notification_id' => $notification->id])); ?>">
                                                <h6 class="mb-1 text-primary"><?php echo e($notification->data['title']); ?></h6>
                                                <p class="mb-0 text-dark"><?php echo e(show_content($notification->data['message'], 60)); ?></p>
                                            </a>
                                            <small class="text-muted"><?php echo e(date_time_ago($notification->created_at)); ?></small>
                                        </div>
                                        <div class="flex-shrink-0 dropdown-notifications-actions">
                                            <a href="<?php echo e(route('administration.notification.destroy', ['notification_id' => $notification->id])); ?>" class="text-danger confirm-danger" data-bs-toggle="tooltip" title="Delete Notification?">
                                                <span class="ti ti-x"></span>
                                            </a>
                                        </div>
                                    </div>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <li class="list-group-item list-group-item-action dropdown-notifications-item">
                                    <?php echo e(__('topnav.no_unread_notification')); ?>

                                </li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <li class="dropdown-menu-footer border-top">
                        <a href="<?php echo e(route('administration.notification.index')); ?>" class="dropdown-item d-flex justify-content-center text-primary p-2 h-px-40 mb-1 align-items-center">
                            <?php echo e(__('topnav.view_all_notification')); ?>

                        </a>
                    </li>
                </ul>
            </li>
            <!--/ Notification -->

            <!-- User -->
            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                        <?php if(auth()->user()->hasMedia('avatar')): ?>
                            <img src="<?php echo e(auth()->user()->getFirstMediaUrl('avatar', 'profile')); ?>" alt="<?php echo e(auth()->user()->name); ?> Avatar" class="rounded-circle" style="height: 40px; width: 40px;">
                        <?php else: ?>
                            <span class="avatar-initial rounded-circle bg-label-hover-dark text-bold">
                                <?php echo e(profile_name_pic(auth()->user())); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('administration.my.profile')); ?>">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <div class="avatar avatar-online" title="<?php echo e(auth()->user()->name); ?>">
                                        <?php if(auth()->user()->hasMedia('avatar')): ?>
                                            <img src="<?php echo e(auth()->user()->getFirstMediaUrl('avatar', 'profile')); ?>" alt="<?php echo e(auth()->user()->name); ?> Avatar" class="rounded-circle" style="height: 40px; width: 40px;">
                                        <?php else: ?>
                                            <span class="avatar-initial rounded-circle bg-label-hover-dark text-bold">
                                                <?php echo e(profile_name_pic(auth()->user())); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <?php if(!is_null(auth()->user()->employee->alias_name)): ?>
                                        <span class="fw-medium d-block"><?php echo e(auth()->user()->employee->alias_name); ?></span>
                                    <?php else: ?>
                                        <span class="fw-medium d-block"><?php echo e(auth()->user()->name); ?></span>
                                    <?php endif; ?>
                                    <small class="text-muted"><?php echo e(auth()->user()->role->name); ?></small>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('administration.my.profile')); ?>">
                            <i class="ti ti-user-check me-2 ti-sm"></i>
                            <span class="align-middle"><?php echo e(__('topnav.my_profile')); ?></span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="<?php echo e(route('administration.my.profile.security')); ?>">
                            <i class="ti ti-lock-cog me-2 ti-sm"></i>
                            <span class="align-middle"><?php echo e(__('topnav.security')); ?></span>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider"></div>
                    </li>
                    <li>
                        <?php if(!session()->has('impersonate')): ?>
                            <a class="dropdown-item" href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="ti ti-logout me-2 ti-sm"></i>
                                <span class="align-middle"><?php echo e(__('topnav.logout')); ?></span>
                            </a>
                            <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                                <?php echo csrf_field(); ?>
                            </form>
                        <?php else: ?>
                            <a class="dropdown-item confirm-warning" href="<?php echo e(route('custom_auth.impersonate.revert')); ?>">
                                <i class="ti ti-logout me-2 ti-sm"></i>
                                <span class="align-middle"><?php echo e(__('Logout & Revert')); ?></span>
                            </a>
                        <?php endif; ?>
                    </li>
                </ul>
            </li>
            <!--/ User -->
        </ul>
    </div>
</nav>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/topnav.blade.php ENDPATH**/ ?>