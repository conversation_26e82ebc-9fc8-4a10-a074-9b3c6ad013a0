<!-- IT Ticket Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Everything', 'IT Ticket Create', 'IT Ticket Read'])): ?>
<li class="menu-item <?php echo e(request()->is('ticket/it_ticket*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-ticket"></i>
        <div data-i18n="IT Ticket"><?php echo e(__('IT Ticket')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Everything'])): ?>
            <li class="menu-item <?php echo e(request()->is('ticket/it_ticket/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.ticket.it_ticket.index')); ?>" class="menu-link"><?php echo e(__('All Tickets')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['IT Ticket Create', 'IT Ticket Read'])): ?>
            <li class="menu-item <?php echo e(request()->is('ticket/it_ticket/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.ticket.it_ticket.my')); ?>" class="menu-link"><?php echo e(__('My Tickets')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('IT Ticket Create')): ?>
            <li class="menu-item <?php echo e(request()->is('ticket/it_ticket/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.ticket.it_ticket.create')); ?>" class="menu-link"><?php echo e(__('Arise New Ticket')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/it_ticket.blade.php ENDPATH**/ ?>