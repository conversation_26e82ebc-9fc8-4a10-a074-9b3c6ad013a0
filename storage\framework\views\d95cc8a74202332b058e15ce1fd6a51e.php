<!-- Daily Work Update -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Daily Work Update Create', 'Daily Work Update Read'])): ?>
<li class="menu-item <?php echo e(request()->is('daily_work_update*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-device-imac-check"></i>
        <div data-i18n="Daily Work Update"><?php echo e(__('Daily Work Update')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if(auth()->user()->hasAllPermissions(['Daily Work Update Create', 'Daily Work Update Update', 'Daily Work Update Delete'])): ?>
            <li class="menu-item <?php echo e(request()->is('daily_work_update/all*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_work_update.index')); ?>" class="menu-link"><?php echo e(__('All Work Updates')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Work Update Read')): ?>
            <li class="menu-item <?php echo e(request()->is('daily_work_update/my*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_work_update.my')); ?>" class="menu-link"><?php echo e(__('My Work Updates')); ?></a>
            </li>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('Daily Work Update Create')): ?>
            <li class="menu-item <?php echo e(request()->is('daily_work_update/create*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.daily_work_update.create')); ?>" class="menu-link"><?php echo e(__('New Work Update')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/daily_work_update.blade.php ENDPATH**/ ?>