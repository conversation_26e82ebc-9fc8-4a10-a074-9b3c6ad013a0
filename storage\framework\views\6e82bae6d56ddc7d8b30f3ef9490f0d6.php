<!-- Booking Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Dining Room Booking Everything', 'Dining Room Booking Create', 'Dining Room Booking Read'])): ?>
<li class="menu-item <?php echo e(request()->is('booking*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-hand-click"></i>
        <div data-i18n="Booking"><?php echo e(__('Booking')); ?></div>
    </a>
    <ul class="menu-sub">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Dining Room Booking Create', 'Dining Room Booking Read'])): ?>
            <li class="menu-item <?php echo e(request()->is('booking/dining_room*') ? 'active' : ''); ?>">
                <a href="<?php echo e(route('administration.booking.dining_room.index')); ?>" class="menu-link"><?php echo e(__('Dining Room Booking')); ?></a>
            </li>
        <?php endif; ?>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/booking.blade.php ENDPATH**/ ?>