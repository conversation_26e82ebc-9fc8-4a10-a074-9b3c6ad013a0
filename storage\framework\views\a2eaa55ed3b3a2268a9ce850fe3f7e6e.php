<!-- Logs -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Logs Read'])): ?>
<li class="menu-header small text-uppercase">
    <span class="menu-header-text"><?php echo e(__('Logs')); ?></span>
</li>

<li class="menu-item <?php echo e(request()->is('logs*') ? 'active open' : ''); ?>">
    <a href="javascript:void(0);" class="menu-link menu-toggle">
        <i class="menu-icon tf-icons ti ti-history"></i>
        <div data-i18n="Logs"><?php echo e(__('Logs')); ?></div>
    </a>
    <ul class="menu-sub">
        <li class="menu-item <?php echo e(request()->is('logs/login_logout_history*') ? 'active' : ''); ?>">
            <a href="<?php echo e(route('administration.logs.login_logout_history.index')); ?>" class="menu-link"><?php echo e(__('Login Histories')); ?></a>
        </li>
    </ul>
</li>
<?php endif; ?>
<?php /**PATH D:\Nigel\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/logs.blade.php ENDPATH**/ ?>